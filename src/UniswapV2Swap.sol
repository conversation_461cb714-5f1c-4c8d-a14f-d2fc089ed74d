// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IUniswapV2Factory} from "@uniswap/v2-core/contracts/interfaces/IUniswapV2Factory.sol";
import {IUniswapV2Router02} from "@uniswap/v2-periphery/contracts/interfaces/IUniswapV2Router02.sol";
import {ReentrancyGuard} from "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

contract UniswapV2Swap is ReentrancyGuard {
    IUniswapV2Factory public immutable factory;
    IUniswapV2Router02 public immutable router;

    // ✅ EVENTS - Critical for DeFi contracts
    event PairCreated(address indexed tokenA, address indexed tokenB, address indexed pair);
    event LiquidityAdded(
        address indexed user,
        address indexed tokenA,
        address indexed tokenB,
        uint256 amountA,
        uint256 amountB,
        uint256 liquidity
    );
    event LiquidityRemoved(
        address indexed user,
        address indexed tokenA,
        address indexed tokenB,
        uint256 amountA,
        uint256 amountB,
        uint256 liquidity
    );
    event TokensSwapped(
        address indexed user,
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        address[] path
    );

    constructor(address _factory, address _router) {
        factory = IUniswapV2Factory(_factory);
        router = IUniswapV2Router02(_router);
    }

    function createPair(address tokenA, address tokenB) external returns (address pair) {
        pair = factory.createPair(tokenA, tokenB);
        emit PairCreated(tokenA, tokenB, pair);
    }

    function getPairAddress(address tokenA, address tokenB) external view returns (address pair) {
        pair = factory.getPair(tokenA, tokenB);
    }

    function addLiquidity(
        address tokenA,
        address tokenB,
        uint256 amountADesired,
        uint256 amountBDesired,
        uint256 amountAMin,
        uint256 amountBMin,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256 amountA, uint256 amountB, uint256 liquidity) {
        // ✅ FIXED: Transfer the DESIRED amounts, not uninitialized return values
        IERC20(tokenA).transferFrom(msg.sender, address(this), amountADesired);
        IERC20(tokenB).transferFrom(msg.sender, address(this), amountBDesired);

        // ✅ FIXED: Approve router to spend our tokens
        IERC20(tokenA).approve(address(router), amountADesired);
        IERC20(tokenB).approve(address(router), amountBDesired);

        // Call Uniswap router
        (amountA, amountB, liquidity) =
            router.addLiquidity(tokenA, tokenB, amountADesired, amountBDesired, amountAMin, amountBMin, to, deadline);

        // ✅ FIXED: Refund unused tokens to user
        if (amountADesired > amountA) {
            IERC20(tokenA).transfer(msg.sender, amountADesired - amountA);
        }
        if (amountBDesired > amountB) {
            IERC20(tokenB).transfer(msg.sender, amountBDesired - amountB);
        }

        emit LiquidityAdded(msg.sender, tokenA, tokenB, amountA, amountB, liquidity);
    }

    function removeLiquidity(
        address tokenA,
        address tokenB,
        uint256 liquidity,
        uint256 amountAMin,
        uint256 amountBMin,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256 amountA, uint256 amountB) {
        address pair = factory.getPair(tokenA, tokenB);
        require(pair != address(0), "Pair does not exist");

        // Transfer LP tokens from user to this contract
        IERC20(pair).transferFrom(msg.sender, address(this), liquidity);

        // Approve router to spend LP tokens
        IERC20(pair).approve(address(router), liquidity);

        // Remove liquidity
        (amountA, amountB) = router.removeLiquidity(tokenA, tokenB, liquidity, amountAMin, amountBMin, to, deadline);

        emit LiquidityRemoved(msg.sender, tokenA, tokenB, amountA, amountB, liquidity);
    }

    function swapExactTokensForTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256[] memory amounts) {
        require(path.length >= 2, "Invalid path");
        require(amountIn > 0, "Invalid amount");
        require(deadline > block.timestamp, "Deadline expired");

        // Transfer input tokens from user
        IERC20(path[0]).transferFrom(msg.sender, address(this), amountIn);

        // Approve router to spend input tokens
        IERC20(path[0]).approve(address(router), amountIn);

        // Execute swap
        amounts = router.swapExactTokensForTokens(amountIn, amountOutMin, path, to, deadline);

        emit TokensSwapped(msg.sender, path[0], path[path.length - 1], amounts[0], amounts[amounts.length - 1], path);
    }

    function swapTokensForExactTokens(
        uint256 amountOut,
        uint256 amountInMax,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256[] memory amounts) {
        require(path.length >= 2, "Invalid path");
        require(amountOut > 0 && amountInMax > 0, "Invalid amounts");
        require(deadline > block.timestamp, "Deadline expired");

        // Transfer max input tokens from user
        IERC20(path[0]).transferFrom(msg.sender, address(this), amountInMax);

        // Approve router to spend input tokens
        IERC20(path[0]).approve(address(router), amountInMax);

        // Execute swap
        amounts = router.swapTokensForExactTokens(amountOut, amountInMax, path, to, deadline);

        // ✅ FIXED: Refund unused input tokens
        uint256 actualAmountIn = amounts[0];
        if (amountInMax > actualAmountIn) {
            IERC20(path[0]).transfer(msg.sender, amountInMax - actualAmountIn);
        }

        emit TokensSwapped(msg.sender, path[0], path[path.length - 1], amounts[0], amounts[amounts.length - 1], path);
    }

    // ✅ ADDED: Utility function to get swap amounts before executing
    function getAmountsOut(uint256 amountIn, address[] calldata path)
        external
        view
        returns (uint256[] memory amounts)
    {
        return router.getAmountsOut(amountIn, path);
    }

    function getAmountsIn(uint256 amountOut, address[] calldata path)
        external
        view
        returns (uint256[] memory amounts)
    {
        return router.getAmountsIn(amountOut, path);
    }

    // ✅ ADDED: Check if pair exists
    function pairExists(address tokenA, address tokenB) external view returns (bool) {
        return factory.getPair(tokenA, tokenB) != address(0);
    }
}
