//SPDX-License-Identifier: MIT

pragma solidity ^0.8.30;

import {Test} from "forge-std/Test.sol";
import {UniswapV2Swap} from "../src/UniswapV2Swap.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {console} from "forge-std/console.sol";

contract UniswapV2SwapTest is Test {
    UniswapV2Swap uniswapV2Swap;
    IERC20 DAI;
    IERC20 USDC;
    IERC20 USDT;
    address constant DAI_ADDRESS = 0x6B175474E89094C44Da98b954EedeAC495271d0F;
    address constant USDC_ADDRESS = 0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48;
    address constant USDT_ADDRESS = 0xdAC17F958D2ee523a2206206994597C13D831ec7;
    address constant sepoliaFactory = 0xF62c03E08ada871A0bEb309762E260a7a6a880E6;
    address constant sepoliaRouter = 0xeE567Fe1712Faf6149d80dA1E6934E354124CfE3;

    function setUp() public {
        uniswapV2Swap = new UniswapV2Swap(sepoliaFactory, sepoliaRouter);
        DAI = IERC20(DAI_ADDRESS);
        USDC = IERC20(USDC_ADDRESS);
        USDT = IERC20(USDT_ADDRESS);
    }

    function testSwapDAIForUSDC() public {
        address[] memory path = new address[](2);
        path[0] = DAI_ADDRESS;
        path[1] = USDC_ADDRESS;

        uint256 amountIn = 1000000000000000000; // 1 DAI
        uint256 amountOutMin = 0; // No minimum

        uint256[] memory amounts = uniswapV2Swap.getAmountsOut(amountIn, path);
        console.log("Amounts out: ", amounts[1]);
    }
}
