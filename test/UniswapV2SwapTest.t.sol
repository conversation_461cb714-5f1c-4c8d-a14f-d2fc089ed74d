//SPDX-License-Identifier: MIT

pragma solidity ^0.8.30;

import {Test} from "forge-std/Test.sol";
import {UniswapV2Swap} from "../src/UniswapV2Swap.sol";
import {Rupee} from "../src/Rupee.sol";
import {Ruble} from "../src/Ruble.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {console} from "forge-std/console.sol";

contract UniswapV2SwapTest is Test {
    UniswapV2Swap uniswapV2Swap;
    Rupee rupee;
    Ruble ruble;

    // ✅ FIXED: Use local Uniswap V2 deployment for testing
    address factory;
    address router;

    address user = address(0x123);
    address liquidityProvider = address(0x456);

    function setUp() public {
        // Deploy our own tokens
        rupee = new Rupee();
        ruble = new Ruble();

        // For testing, we'll use Sepolia addresses (you can also deploy local Uniswap)
        factory = 0xF62c03E08ada871A0bEb309762E260a7a6a880E6;
        router = 0xeE567Fe1712Faf6149d80dA1E6934E354124CfE3;

        uniswapV2Swap = new UniswapV2Swap(factory, router);

        // Give tokens to test users
        rupee.transfer(user, 10000 * 10 ** 18); // 10k Rupee
        ruble.transfer(user, 10000 * 10 ** 18); // 10k ruble

        rupee.transfer(liquidityProvider, 50000 * 10 ** 18); // 50k Rupee
        ruble.transfer(liquidityProvider, 50000 * 10 ** 18); // 50k ruble
    }

    function testCreatePair() public {
        address pair = uniswapV2Swap.createPair(address(rupee), address(ruble));
        assertTrue(pair != address(0), "Pair should be created");

        address retrievedPair = uniswapV2Swap.getPairAddress(address(rupee), address(ruble));
        assertEq(pair, retrievedPair, "Pair addresses should match");
    }

    function testAddLiquidity() public {
        // First create pair
        uniswapV2Swap.createPair(address(rupee), address(ruble));

        vm.startPrank(liquidityProvider);

        // Approve tokens
        rupee.approve(address(uniswapV2Swap), 1000 * 10 ** 18);
        ruble.approve(address(uniswapV2Swap), 1000 * 10 ** 6);

        // Add liquidity
        (uint256 amountA, uint256 amountB, uint256 liquidity) = uniswapV2Swap.addLiquidity(
            address(rupee),
            address(ruble),
            1000 * 10 ** 18, // 1000 Rupee
            1000 * 10 ** 18, // 1000 ruble
            900 * 10 ** 18, // Min 900 Rupee
            900 * 10 ** 18, // Min 900 ruble
            liquidityProvider,
            block.timestamp + 300
        );

        assertTrue(liquidity > 0, "Should receive LP tokens");
        console.log("LP tokens received:", liquidity);

        vm.stopPrank();
    }

    function testSwapRupeeForruble() public {
        // Setup: Create pair and add liquidity first
        testAddLiquidity();

        vm.startPrank(user);

        address[] memory path = new address[](2);
        path[0] = address(rupee); // ✅ Using YOUR token!
        path[1] = address(ruble);

        uint256 amountIn = 100 * 10 ** 18; // 100 Rupee

        // Check expected output
        uint256[] memory expectedAmounts = uniswapV2Swap.getAmountsOut(amountIn, path);
        console.log("Expected ruble out:", expectedAmounts[1]);

        // Approve and swap
        rupee.approve(address(uniswapV2Swap), amountIn);

        uint256[] memory amounts = uniswapV2Swap.swapExactTokensForTokens(
            amountIn,
            0, // Accept any amount
            path,
            user,
            block.timestamp + 300
        );

        console.log("Actual Rupee in:", amounts[0]);
        console.log("Actual ruble out:", amounts[1]);

        assertTrue(amounts[1] > 0, "Should receive ruble");

        vm.stopPrank();
    }
}
