// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {UniswapV2Swap} from "../src/UniswapV2Swap.sol";
import {Script} from "forge-std/Script.sol";

contract DeploySwap is Script {
    address constant sepoliaFactory = 0xF62c03E08ada871A0bEb309762E260a7a6a880E6;
    address constant sepoliaRouter = 0xeE567Fe1712Faf6149d80dA1E6934E354124CfE3;

    function run() external returns (UniswapV2Swap) {
        vm.startBroadcast();
        UniswapV2Swap swap = new UniswapV2Swap(sepoliaFactory, sepoliaRouter);
        vm.stopBroadcast();
        return swap;
    }
}
