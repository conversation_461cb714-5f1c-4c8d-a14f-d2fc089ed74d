{"name": "@uniswap/v2-core", "description": "🎛 Core contracts for the UniswapV2 protocol", "version": "1.0.1", "homepage": "https://uniswap.org", "repository": {"type": "git", "url": "https://github.com/Uniswap/uniswap-v2-core"}, "keywords": ["uniswap", "ethereum", "v2", "core", "uniswap-v2"], "files": ["contracts", "build"], "engines": {"node": ">=10"}, "devDependencies": {"@types/chai": "^4.2.6", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "ethereum-waffle": "^2.4.1", "ethereumjs-util": "^6.2.0", "mocha": "^6.2.2", "prettier": "^1.19.1", "rimraf": "^3.0.0", "solc": "0.5.16", "ts-node": "^8.5.4", "typescript": "^3.7.3"}, "scripts": {"lint": "yarn prettier ./test/*.ts --check", "lint:fix": "yarn prettier ./test/*.ts --write", "clean": "rimraf ./build/", "precompile": "yarn clean", "compile": "waffle .waffle.json", "pretest": "yarn compile", "test": "mocha", "prepublishOnly": "yarn test"}, "license": "GPL-3.0-or-later"}