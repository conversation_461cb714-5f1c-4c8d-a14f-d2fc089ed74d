# Configuration for probot-stale - https://github.com/probot/stale

issues:
  # Number of days of inactivity before an Issue or Pull Request becomes stale
  daysUntilStale: 7 

  # Number of days of inactivity before an Issue or Pull Request with the stale label is closed.
  # Set to false to disable. If disabled, issues still need to be closed manually, but will remain marked as stale.
  daysUntilClose: 7

  # Only issues or pull requests with all of these labels are check if stale. Defaults to `[]` (disabled)
  onlyLabels:
    - question
    - autoclose

  # Issues or Pull Requests with these labels will never be considered stale. Set to `[]` to disable
  exemptLabels:
    - p0
    - bug

  # Comment to post when marking as stale. Set to `false` to disable
  markComment: >
    This issue has been automatically marked as stale because it has not had
    recent activity. It will be closed if no further activity occurs. Thank you
    for your contributions.
