name: CI

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  test:
    strategy:
      matrix:
        node: ['10.x', '12.x']
        os: [ubuntu-latest]

    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node }}

      - run: npm install -g yarn

      - id: yarn-cache
        run: echo "::set-output name=dir::$(yarn cache dir)"
      - uses: actions/cache@v1
        with:
          path: ${{ steps.yarn-cache.outputs.dir }}
          key: ${{ matrix.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ matrix.os }}-yarn-

      - run: yarn
      - run: yarn lint
      - run: yarn test
