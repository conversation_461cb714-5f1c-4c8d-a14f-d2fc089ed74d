name: Setup
description: Common environment setup

runs:
  using: composite
  steps:
    - uses: actions/setup-node@v4
      with:
        node-version: 22.x
    - uses: actions/cache@v4
      id: cache
      with:
        path: '**/node_modules'
        key: npm-v3-${{ hashFiles('**/package-lock.json') }}
    - name: Install dependencies
      run: npm ci
      shell: bash
      if: steps.cache.outputs.cache-hit != 'true'
    - name: Install Foundry
      uses: foundry-rs/foundry-toolchain@v1
      with:
        version: stable
