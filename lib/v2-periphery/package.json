{"name": "@uniswap/v2-periphery", "version": "1.1.0-beta.0", "description": "🎚 Peripheral smart contracts for interacting with Uniswap V2", "engines": {"node": ">=10"}, "homepage": "https://uniswap.org", "repository": {"type": "git", "url": "https://github.com/Uniswap/uniswap-v2-periphery"}, "files": ["build", "contracts"], "dependencies": {"@uniswap/lib": "4.0.1-alpha", "@uniswap/v2-core": "1.0.0"}, "devDependencies": {"@types/chai": "^4.2.6", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "ethereum-waffle": "^2.4.1", "ethereumjs-util": "^6.2.0", "mocha": "^6.2.2", "ncp": "^2.0.0", "prettier": "^1.19.1", "rimraf": "^3.0.0", "solc": "0.6.6", "ts-node": "^8.5.4", "typescript": "^3.7.3"}, "scripts": {"lint": "yarn prettier ./test/*.ts --check", "lint:fix": "yarn prettier ./test/*.ts --write", "clean": "rimraf ./build/", "copy-v1-artifacts": "ncp ./buildV1 ./build", "precompile": "yarn clean", "compile": "waffle .waffle.json", "postcompile": "yarn copy-v1-artifacts", "pretest": "yarn compile", "test": "mocha", "prepublishOnly": "yarn test"}, "license": "GPL-3.0-or-later"}